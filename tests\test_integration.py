"""
Integration tests for AI Utility Orchestrator.
These tests verify that all components work together correctly.
"""

import pytest
import tempfile
import json
from pathlib import Path
from unittest.mock import patch, Mock
from ai_utility_orchestrator.core.agent_builder import agent_executor
from ai_utility_orchestrator.utils.toolkit import ConfigUtils


@pytest.mark.integration
class TestFullIntegration:
    """Integration tests for the complete system."""
    
    def create_integration_config(self, temp_dir):
        """Create a complete configuration for integration testing."""
        return {
            "llm": {
                "model": "gpt-4o-mini",
                "temperature": 0.5,
                "max_tokens": 800
            },
            "system_prompt": "You are an AI Orchestrator for integration testing.",
            "default_user_id": "integration_test_user",
            "context_limit": 2,
            "enable_parameter_enhancement": False,
            "enable_ai_response_generation": False,
            "context_storage": {
                "backend": "file",
                "file_path": str(Path(temp_dir) / "integration_history.json")
            },
            "context_format": {
                "user_role": "user",
                "assistant_role": "assistant",
                "include_metadata": False
            },
            "registry_config": {
                "registration_message": "✅ Registered: {tool_name}"
            },
            "tools": [
                {
                    "name": "search_tool",
                    "description": "Search for information",
                    "execute_func": "core.tools.search_execute",
                    "schema": {
                        "type": "object",
                        "properties": {
                            "query": {"type": "string", "description": "Search query"},
                            "search_type": {"type": "string", "enum": ["web", "academic", "news"], "default": "web"},
                            "max_results": {"type": "integer", "default": 5}
                        },
                        "required": ["query"]
                    }
                },
                {
                    "name": "registry_tool",
                    "description": "List available tools",
                    "execute_func": "core.tools.registry_query_tool_execute",
                    "schema": {
                        "type": "object",
                        "properties": {
                            "output_format": {"type": "string", "enum": ["list", "json", "detailed"], "default": "list"},
                            "include_descriptions": {"type": "boolean", "default": False}
                        }
                    }
                }
            ],
            "error_messages": {
                "tool_execution_failed": "Tool {tool_name} failed: {error}",
                "tool_not_found": "Tool {tool_name} not found",
                "general_error": "Integration test error: {error}"
            },
            "default_responses": {
                "no_tool_needed": "Direct response from integration test."
            }
        }
    
    @patch('ai_utility_orchestrator.utils.response_formatter.client')
    def test_complete_workflow_direct_response(self, mock_client):
        """Test complete workflow with direct response."""
        with tempfile.TemporaryDirectory() as temp_dir:
            config = self.create_integration_config(temp_dir)
            
            # Mock OpenAI response for direct answer
            mock_response = Mock()
            mock_response.choices = [Mock()]
            mock_response.choices[0].message.content = '{"selected_tool": "none", "parameters": {}, "reasoning": "This is a general knowledge question", "direct_response": "The capital of France is Paris."}'
            mock_client.chat.completions.create.return_value = mock_response
            
            result = agent_executor("What is the capital of France?", config=config)
            
            assert result is not None
            assert result["selected_tool"] == "none"
            assert result["final_response"] == "The capital of France is Paris."
            assert result["user_id"] == "integration_test_user"
            assert "error" not in result
    
    @patch('ai_utility_orchestrator.utils.response_formatter.client')
    def test_complete_workflow_with_search_tool(self, mock_client):
        """Test complete workflow using search tool."""
        with tempfile.TemporaryDirectory() as temp_dir:
            config = self.create_integration_config(temp_dir)
            
            # Mock OpenAI responses
            mock_responses = [
                # Tool selection response
                Mock(),
                # Final response generation
                Mock()
            ]
            
            mock_responses[0].choices = [Mock()]
            mock_responses[0].choices[0].message.content = '{"selected_tool": "search_tool", "parameters": {"query": "Python programming tutorials", "search_type": "web", "max_results": 5}, "reasoning": "User wants to search for information", "direct_response": ""}'
            
            mock_responses[1].choices = [Mock()]
            mock_responses[1].choices[0].message.content = "I found several Python programming tutorials for you. Here are the top 5 web results that should help you get started with Python programming."
            
            mock_client.chat.completions.create.side_effect = mock_responses
            
            result = agent_executor("Find me Python programming tutorials", config=config)
            
            assert result is not None
            assert result["selected_tool"] == "search_tool"
            assert result["tool_parameters"]["query"] == "Python programming tutorials"
            assert result["tool_parameters"]["search_type"] == "web"
            assert result["tool_parameters"]["max_results"] == 5
            assert "Python programming tutorials" in result["tool_result"]
            assert "tutorials" in result["final_response"]
    
    @patch('ai_utility_orchestrator.utils.response_formatter.client')
    def test_complete_workflow_with_registry_tool(self, mock_client):
        """Test complete workflow using registry tool."""
        with tempfile.TemporaryDirectory() as temp_dir:
            config = self.create_integration_config(temp_dir)
            
            # Mock OpenAI responses
            mock_responses = [
                # Tool selection response
                Mock(),
                # Final response generation
                Mock()
            ]
            
            mock_responses[0].choices = [Mock()]
            mock_responses[0].choices[0].message.content = '{"selected_tool": "registry_tool", "parameters": {"output_format": "detailed", "include_descriptions": true}, "reasoning": "User wants to know available tools", "direct_response": ""}'
            
            mock_responses[1].choices = [Mock()]
            mock_responses[1].choices[0].message.content = "Here are the available tools in the system: search_tool for searching information and registry_tool for listing tools."
            
            mock_client.chat.completions.create.side_effect = mock_responses
            
            result = agent_executor("What tools are available?", config=config)
            
            assert result is not None
            assert result["selected_tool"] == "registry_tool"
            assert result["tool_parameters"]["output_format"] == "detailed"
            assert result["tool_parameters"]["include_descriptions"] is True
            assert "search_tool" in result["tool_result"]
            assert "registry_tool" in result["tool_result"]
    
    @patch('ai_utility_orchestrator.utils.response_formatter.client')
    def test_conversation_context_persistence(self, mock_client):
        """Test that conversation context is maintained across multiple interactions."""
        with tempfile.TemporaryDirectory() as temp_dir:
            config = self.create_integration_config(temp_dir)
            user_id = "context_test_user"
            
            # Mock OpenAI responses for multiple interactions
            mock_response = Mock()
            mock_response.choices = [Mock()]
            
            # First interaction
            mock_response.choices[0].message.content = '{"selected_tool": "none", "parameters": {}, "reasoning": "Greeting", "direct_response": "Hello! How can I help you today?"}'
            mock_client.chat.completions.create.return_value = mock_response
            
            result1 = agent_executor("Hello", config=config, user_id=user_id)
            assert result1["final_response"] == "Hello! How can I help you today?"
            
            # Second interaction - should have context from first
            mock_response.choices[0].message.content = '{"selected_tool": "none", "parameters": {}, "reasoning": "Follow-up response", "direct_response": "I can help you with searches and tool information."}'
            
            result2 = agent_executor("What can you do?", config=config, user_id=user_id)
            assert result2["final_response"] == "I can help you with searches and tool information."
            
            # Verify that context was passed (check call arguments)
            calls = mock_client.chat.completions.create.call_args_list
            second_call_messages = calls[1][1]["messages"]
            
            # Should include previous conversation in context
            assert len(second_call_messages) >= 3  # system + previous user + previous assistant + current user
    
    @patch('ai_utility_orchestrator.utils.response_formatter.client')
    def test_error_handling_integration(self, mock_client):
        """Test error handling in the complete workflow."""
        with tempfile.TemporaryDirectory() as temp_dir:
            config = self.create_integration_config(temp_dir)
            
            # Mock API error
            mock_client.chat.completions.create.side_effect = Exception("API connection failed")
            
            result = agent_executor("Test query", config=config)
            
            assert result is not None
            assert result["selected_tool"] == "none"
            assert "error" in result
            assert "API connection failed" in result["error"]
            assert "Integration test error" in result["final_response"]
    
    def test_config_loading_integration(self):
        """Test configuration loading and validation."""
        with tempfile.TemporaryDirectory() as temp_dir:
            config_data = self.create_integration_config(temp_dir)
            
            # Save config to file
            config_file = Path(temp_dir) / "test_config.json"
            with open(config_file, 'w') as f:
                json.dump(config_data, f, indent=2)
            
            # Load config using ConfigUtils
            loaded_config = ConfigUtils.load_config(str(config_file))
            
            assert loaded_config["llm"]["model"] == "gpt-4o-mini"
            assert loaded_config["default_user_id"] == "integration_test_user"
            assert len(loaded_config["tools"]) == 2
            assert loaded_config["tools"][0]["name"] == "search_tool"
            assert loaded_config["tools"][1]["name"] == "registry_tool"
    
    @patch('ai_utility_orchestrator.utils.response_formatter.client')
    def test_custom_user_configuration(self, mock_client):
        """Test integration with custom user configuration."""
        with tempfile.TemporaryDirectory() as temp_dir:
            base_config = self.create_integration_config(temp_dir)
            
            # Create user-specific config
            user_config = {
                "llm": {"temperature": 0.9},
                "context_limit": 1,
                "custom_user_setting": "test_value"
            }
            
            user_config_file = Path(temp_dir) / "user_custom_user.json"
            with open(user_config_file, 'w') as f:
                json.dump(user_config, f)
            
            # Save base config
            base_config_file = Path(temp_dir) / "base_config.json"
            with open(base_config_file, 'w') as f:
                json.dump(base_config, f)
            
            # Load with user override
            final_config = ConfigUtils.load_config(str(base_config_file), user_id="custom_user")
            
            assert final_config["llm"]["temperature"] == 0.9  # User override
            assert final_config["llm"]["model"] == "gpt-4o-mini"  # Base config
            assert final_config["context_limit"] == 1  # User override
            assert final_config["custom_user_setting"] == "test_value"  # User addition
