#!/usr/bin/env python3
"""
AI Utility Orchestrator - Installation Validation Script

This standalone script validates that the AI Utility Orchestrator package
is properly installed and working correctly.

Usage:
    python validate_ai_utility_orchestrator.py

Requirements:
    - ai_utility_orchestrator package must be installed
    - No additional dependencies required (uses mocking for external calls)

This script tests the package from an external user perspective.
"""

import sys
import os
import tempfile
import json
from pathlib import Path
from unittest.mock import Mock, patch

def print_header(title):
    """Print a formatted header."""
    print(f"\n{'='*60}")
    print(f" {title}")
    print(f"{'='*60}")

def print_test(test_name, status="RUNNING"):
    """Print test status."""
    if status == "RUNNING":
        print(f"🔄 {test_name}...", end=" ")
    elif status == "PASS":
        print("✅ PASSED")
    elif status == "FAIL":
        print("❌ FAILED")

def validate_imports():
    """Test 1: Validate that all package components can be imported."""
    print_test("Testing package imports", "RUNNING")
    
    try:
        # Test main package import
        import ai_utility_orchestrator
        
        # Test main components
        from ai_utility_orchestrator import agent_executor, ToolRegistry, Tool, ConfigUtils
        
        # Test utility components
        from ai_utility_orchestrator.utils.context_manager import ContextManager
        from ai_utility_orchestrator.utils.response_formatter import format_response
        
        # Test version
        assert hasattr(ai_utility_orchestrator, '__version__')
        
        print_test("Testing package imports", "PASS")
        return True
        
    except Exception as e:
        print_test("Testing package imports", "FAIL")
        print(f"   Error: {e}")
        return False

def validate_tool_system():
    """Test 2: Validate tool creation and registry functionality."""
    print_test("Testing tool system", "RUNNING")
    
    try:
        from ai_utility_orchestrator import Tool, ToolRegistry
        
        # Create a simple test tool
        def test_tool_func(params):
            return f"Test result: {params.get('input', 'no input')}"
        
        tool = Tool(
            name="validation_tool",
            description="A tool for validation testing",
            execute_func=test_tool_func,
            schema={"type": "object", "properties": {"input": {"type": "string"}}}
        )
        
        # Test tool properties
        assert tool.name == "validation_tool"
        assert callable(tool.execute)
        
        # Test registry
        registry = ToolRegistry()
        registry.register_tool(tool)
        
        tools = registry.get_tools()
        assert len(tools) == 1
        assert tools[0].name == "validation_tool"
        
        # Test tool execution
        result = tool.execute({"input": "validation test"})
        assert "validation test" in result
        
        print_test("Testing tool system", "PASS")
        return True
        
    except Exception as e:
        print_test("Testing tool system", "FAIL")
        print(f"   Error: {e}")
        return False

def validate_configuration():
    """Test 3: Validate configuration loading."""
    print_test("Testing configuration system", "RUNNING")
    
    try:
        from ai_utility_orchestrator import ConfigUtils
        
        # Test default config loading
        config = ConfigUtils.load_config()
        assert isinstance(config, dict)
        assert "llm" in config
        assert "system_prompt" in config
        
        # Test custom config loading
        with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
            test_config = {
                "test_setting": "validation_value",
                "llm": {"model": "test-model", "temperature": 0.8}
            }
            json.dump(test_config, f)
            temp_path = f.name
        
        try:
            custom_config = ConfigUtils.load_config(temp_path)
            assert custom_config["test_setting"] == "validation_value"
            assert custom_config["llm"]["model"] == "test-model"
        finally:
            os.unlink(temp_path)
        
        print_test("Testing configuration system", "PASS")
        return True
        
    except Exception as e:
        print_test("Testing configuration system", "FAIL")
        print(f"   Error: {e}")
        return False

def validate_context_management():
    """Test 4: Validate context management."""
    print_test("Testing context management", "RUNNING")
    
    try:
        from ai_utility_orchestrator.utils.context_manager import ContextManager
        
        with tempfile.TemporaryDirectory() as temp_dir:
            history_path = Path(temp_dir) / "validation_history.json"
            cm = ContextManager(str(history_path))
            
            # Test adding interactions
            cm.add_interaction("validation_user", "Hello", "Hi there!")
            cm.add_interaction("validation_user", "How are you?", "I'm good!")
            
            # Test retrieving history
            history = cm.get_history("validation_user")
            assert len(history) == 2
            assert history[0]["user"] == "Hello"
            assert history[1]["bot"] == "I'm good!"
            
            # Test message formatting
            messages = cm.get_recent_messages("validation_user", limit=1)
            assert len(messages) == 2  # 1 interaction = 2 messages
            
        print_test("Testing context management", "PASS")
        return True
        
    except Exception as e:
        print_test("Testing context management", "FAIL")
        print(f"   Error: {e}")
        return False

def validate_response_formatting():
    """Test 5: Validate response formatting (with mocking)."""
    print_test("Testing response formatting", "RUNNING")
    
    try:
        from ai_utility_orchestrator.utils.response_formatter import format_response
        
        # Mock the OpenAI client to avoid external dependencies
        with patch('ai_utility_orchestrator.utils.response_formatter.client') as mock_client:
            # Mock successful response
            mock_response = Mock()
            mock_response.choices = [Mock()]
            mock_response.choices[0].message.content = "Validation test response"
            mock_client.chat.completions.create.return_value = mock_response
            
            # Test basic formatting
            result = format_response("Test prompt", formatter="answer")
            assert result == "Validation test response"
            
            # Test JSON formatting
            mock_response.choices[0].message.content = '{"status": "success", "data": "validation"}'
            result = format_response("Test prompt", formatter="json")
            assert isinstance(result, dict)
            assert result["status"] == "success"
            
            # Test with metadata
            result = format_response("Test prompt", return_meta=True)
            assert isinstance(result, dict)
            assert "raw_response" in result
            assert "parsed_response" in result
        
        print_test("Testing response formatting", "PASS")
        return True
        
    except Exception as e:
        print_test("Testing response formatting", "FAIL")
        print(f"   Error: {e}")
        return False

def validate_agent_executor():
    """Test 6: Validate the main agent executor functionality."""
    print_test("Testing agent executor", "RUNNING")
    
    try:
        from ai_utility_orchestrator import agent_executor
        
        # Create test configuration
        test_config = {
            "llm": {"model": "gpt-4o-mini", "temperature": 0.5},
            "system_prompt": "You are a validation assistant.",
            "default_user_id": "validation_user",
            "context_limit": 2,
            "context_storage": {"backend": "file", "file_path": None},
            "tools": [
                {
                    "name": "validation_calculator",
                    "description": "A calculator for validation",
                    "execute_func": "validate_ai_utility_orchestrator.mock_calculator",
                    "schema": {
                        "type": "object",
                        "properties": {"expression": {"type": "string"}},
                        "required": ["expression"]
                    }
                }
            ]
        }
        
        # Mock LLM response for direct answer
        with patch('ai_utility_orchestrator.utils.response_formatter.client') as mock_client:
            mock_response = Mock()
            mock_response.choices = [Mock()]
            mock_response.choices[0].message.content = '{"selected_tool": "none", "parameters": {}, "reasoning": "Direct answer", "direct_response": "Validation successful!"}'
            mock_client.chat.completions.create.return_value = mock_response
            
            # Test direct response
            result = agent_executor("Hello", config=test_config)
            assert result["selected_tool"] == "none"
            assert "Validation successful!" in result["final_response"]
            
            # Test tool execution
            mock_response.choices[0].message.content = '{"selected_tool": "validation_calculator", "parameters": {"expression": "2+2"}, "reasoning": "Using calculator", "direct_response": ""}'
            result = agent_executor("What is 2+2?", config=test_config)
            assert result["selected_tool"] == "validation_calculator"
            assert "4" in result["tool_result"]
        
        print_test("Testing agent executor", "PASS")
        return True
        
    except Exception as e:
        print_test("Testing agent executor", "FAIL")
        print(f"   Error: {e}")
        return False

def mock_calculator(params):
    """Mock calculator function for validation testing."""
    expression = params.get("expression", "")
    if "2+2" in expression or "2 + 2" in expression:
        return "2+2 = 4"
    return f"Calculated: {expression}"

def main():
    """Run all validation tests."""
    print_header("AI Utility Orchestrator - Installation Validation")
    print("This script validates that the AI Utility Orchestrator package")
    print("is properly installed and functioning correctly.")
    
    # Run all validation tests
    tests = [
        validate_imports,
        validate_tool_system,
        validate_configuration,
        validate_context_management,
        validate_response_formatting,
        validate_agent_executor
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
    
    # Print summary
    print_header("Validation Summary")
    print(f"Tests passed: {passed}/{total}")
    
    if passed == total:
        print("🎉 SUCCESS: AI Utility Orchestrator is properly installed and working!")
        print("\nYou can now use the package in your projects:")
        print("   from ai_utility_orchestrator import agent_executor")
        print("   result = agent_executor('Your query here')")
        return 0
    else:
        print("❌ FAILURE: Some tests failed. Please check the installation.")
        print("\nTry reinstalling the package:")
        print("   pip uninstall ai_utility_orchestrator")
        print("   pip install ai_utility_orchestrator")
        return 1

if __name__ == "__main__":
    sys.exit(main())
