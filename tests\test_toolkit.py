"""
Tests for utils.toolkit module.
"""

import pytest
import json
import tempfile
import os
from pathlib import Path
from unittest.mock import patch, Mock
from ai_utility_orchestrator.utils.toolkit import ConfigUtils, EnvUtils, LLMUtils, DynamicImportUtils, RegistryUtils


class TestConfigUtils:
    """Test the ConfigUtils class."""
    
    def test_load_config_from_file(self, temp_config_file, sample_config):
        """Test loading config from a specific file."""
        config = ConfigUtils.load_config(temp_config_file)
        
        assert config["llm"]["model"] == sample_config["llm"]["model"]
        assert config["default_user_id"] == sample_config["default_user_id"]
        assert len(config["tools"]) == len(sample_config["tools"])
    
    def test_load_config_with_overrides(self, temp_config_file):
        """Test loading config with overrides."""
        overrides = {
            "llm": {"model": "gpt-4", "temperature": 0.1},
            "custom_setting": "test_value"
        }
        
        config = ConfigUtils.load_config(temp_config_file, overrides=overrides)
        
        assert config["llm"]["model"] == "gpt-4"
        assert config["llm"]["temperature"] == 0.1
        assert config["custom_setting"] == "test_value"
    
    def test_load_config_nonexistent_file(self):
        """Test loading config when file doesn't exist."""
        config = ConfigUtils.load_config("nonexistent_file.json")
        
        # Should return default config
        assert "llm" in config
        assert config["llm"]["model"] == "gpt-4o-mini"
        assert "system_prompt" in config
        assert "tools" in config
    
    def test_load_config_auto_discovery(self, temp_config_file, sample_config):
        """Test automatic config file discovery."""
        # Create a temporary directory structure
        with tempfile.TemporaryDirectory() as temp_dir:
            config_dir = Path(temp_dir) / "config"
            config_dir.mkdir()
            
            config_file = config_dir / "config_default.json"
            with open(config_file, 'w') as f:
                json.dump(sample_config, f)
            
            # Change to temp directory
            original_cwd = os.getcwd()
            try:
                os.chdir(temp_dir)
                config = ConfigUtils.load_config()
                
                assert config["llm"]["model"] == sample_config["llm"]["model"]
            finally:
                os.chdir(original_cwd)
    
    def test_load_config_with_user_config(self, temp_config_file, sample_config):
        """Test loading config with user-specific overrides."""
        # Create user config file
        user_config = {"llm": {"temperature": 0.9}, "user_setting": "custom"}
        user_config_path = Path(temp_config_file).parent / "user_test_user.json"
        
        with open(user_config_path, 'w') as f:
            json.dump(user_config, f)
        
        try:
            config = ConfigUtils.load_config(temp_config_file, user_id="test_user")
            
            assert config["llm"]["temperature"] == 0.9
            assert config["user_setting"] == "custom"
            # Original settings should still be there
            assert config["llm"]["model"] == sample_config["llm"]["model"]
        finally:
            if user_config_path.exists():
                user_config_path.unlink()
    
    def test_load_tools_from_config(self, sample_config):
        """Test loading tools from config."""
        tools = ConfigUtils.load_tools_from_config(sample_config)
        
        assert len(tools) == 1
        assert tools[0]["name"] == "test_tool"
        assert tools[0]["description"] == "A test tool for unit testing"
    
    def test_load_tools_from_empty_config(self):
        """Test loading tools from config with no tools."""
        config = {}
        tools = ConfigUtils.load_tools_from_config(config)
        
        assert tools == []


class TestEnvUtils:
    """Test the EnvUtils class."""
    
    def test_get_env_existing(self):
        """Test getting an existing environment variable."""
        with patch.dict(os.environ, {"TEST_VAR": "test_value"}):
            result = EnvUtils.get_env("TEST_VAR")
            assert result == "test_value"
    
    def test_get_env_nonexistent_with_default(self):
        """Test getting a nonexistent environment variable with default."""
        result = EnvUtils.get_env("NONEXISTENT_VAR", "default_value")
        assert result == "default_value"
    
    def test_get_env_nonexistent_no_default(self):
        """Test getting a nonexistent environment variable without default."""
        result = EnvUtils.get_env("NONEXISTENT_VAR")
        assert result == ""
    
    def test_get_env_required_existing(self):
        """Test getting a required environment variable that exists."""
        with patch.dict(os.environ, {"REQUIRED_VAR": "required_value"}):
            result = EnvUtils.get_env("REQUIRED_VAR", required=True)
            assert result == "required_value"
    
    def test_get_env_required_missing(self):
        """Test getting a required environment variable that doesn't exist."""
        with pytest.raises(EnvironmentError, match="Missing required environment variable"):
            EnvUtils.get_env("MISSING_REQUIRED_VAR", required=True)


class TestLLMUtils:
    """Test the LLMUtils class."""
    
    def test_set_model(self):
        """Test setting model configuration."""
        original_model = LLMUtils.model_name
        original_temp = LLMUtils.temperature
        
        try:
            LLMUtils.set_model("gpt-4", 0.2)
            
            assert LLMUtils.model_name == "gpt-4"
            assert LLMUtils.temperature == 0.2
        finally:
            # Restore original values
            LLMUtils.model_name = original_model
            LLMUtils.temperature = original_temp
    
    def test_generate_response(self):
        """Test generating a mock response."""
        LLMUtils.set_model("test-model", 0.5)
        
        response = LLMUtils.generate_response("Test prompt")
        
        assert "test-model" in response
        assert "0.5" in response
        assert "Test prompt" in response


class TestDynamicImportUtils:
    """Test the DynamicImportUtils class."""
    
    def test_load_object_success(self):
        """Test successfully loading an object."""
        # Load a standard library function
        result = DynamicImportUtils.load_object("json.dumps")
        
        assert result == json.dumps
    
    def test_load_object_module_not_found(self):
        """Test loading from a nonexistent module."""
        result = DynamicImportUtils.load_object("nonexistent_module.function")
        
        assert result is None
    
    def test_load_object_attribute_not_found(self):
        """Test loading a nonexistent attribute."""
        result = DynamicImportUtils.load_object("json.nonexistent_function")
        
        assert result is None
    
    def test_load_object_invalid_path(self):
        """Test loading with invalid path format."""
        result = DynamicImportUtils.load_object("invalid_path")
        
        assert result is None


class TestRegistryUtils:
    """Test the RegistryUtils class."""
    
    def setUp(self):
        """Clear registered tools before each test."""
        RegistryUtils.registered_tools.clear()
    
    def test_register_tool_decorator(self):
        """Test registering a tool using the decorator."""
        self.setUp()
        
        @RegistryUtils.register_tool("test_tool")
        def test_function():
            return "test result"
        
        assert "test_tool" in RegistryUtils.registered_tools
        assert RegistryUtils.registered_tools["test_tool"] == test_function
    
    def test_get_tool_existing(self):
        """Test getting an existing tool."""
        self.setUp()
        
        def test_function():
            return "test result"
        
        RegistryUtils.registered_tools["existing_tool"] = test_function
        
        result = RegistryUtils.get_tool("existing_tool")
        assert result == test_function
    
    def test_get_tool_nonexistent(self):
        """Test getting a nonexistent tool."""
        self.setUp()
        
        result = RegistryUtils.get_tool("nonexistent_tool")
        assert result is None
    
    def test_list_registered_tools(self):
        """Test listing all registered tools."""
        self.setUp()
        
        def tool1():
            pass
        
        def tool2():
            pass
        
        RegistryUtils.registered_tools["tool1"] = tool1
        RegistryUtils.registered_tools["tool2"] = tool2
        
        tools = RegistryUtils.list_registered_tools()
        
        assert len(tools) == 2
        assert "tool1" in tools
        assert "tool2" in tools
    
    def test_list_registered_tools_empty(self):
        """Test listing tools when none are registered."""
        self.setUp()
        
        tools = RegistryUtils.list_registered_tools()
        assert tools == []
