"""
Pytest configuration and fixtures for AI Utility Orchestrator tests.
"""

import pytest
import json
import tempfile
import os
from pathlib import Path
from unittest.mock import Mock, patch
from ai_utility_orchestrator.core.agent_registry import ToolRegistry
from ai_utility_orchestrator.core.tools import Tool
from ai_utility_orchestrator.utils.context_manager import Context<PERSON>anager
from ai_utility_orchestrator.utils.toolkit import ConfigUtils


@pytest.fixture
def mock_openai_client():
    """Mock OpenAI client for testing without API calls."""
    with patch('ai_utility_orchestrator.utils.response_formatter.client') as mock_client:
        mock_response = Mock()
        mock_response.choices = [Mock()]
        mock_response.choices[0].message.content = '{"selected_tool": "none", "parameters": {}, "reasoning": "test", "direct_response": "Test response"}'
        
        mock_client.chat.completions.create.return_value = mock_response
        yield mock_client


@pytest.fixture
def sample_config():
    """Sample configuration for testing."""
    return {
        "llm": {
            "model": "gpt-4o-mini",
            "temperature": 0.5,
            "max_tokens": 800
        },
        "system_prompt": "You are a test AI assistant.",
        "default_user_id": "test_user",
        "context_limit": 3,
        "enable_parameter_enhancement": False,
        "enable_ai_response_generation": False,
        "context_storage": {
            "backend": "file",
            "file_path": "test_data/chat_history.json"
        },
        "context_format": {
            "user_role": "user",
            "assistant_role": "assistant",
            "include_metadata": False
        },
        "registry_config": {
            "registration_message": "Registered tool: {tool_name}"
        },
        "tools": [
            {
                "name": "test_tool",
                "description": "A test tool for unit testing",
                "execute_func": "tests.test_tools.mock_tool_execute",
                "schema": {
                    "type": "object",
                    "properties": {
                        "query": {"type": "string", "description": "Test query"}
                    },
                    "required": ["query"]
                }
            }
        ],
        "error_messages": {
            "tool_execution_failed": "Tool {tool_name} failed: {error}",
            "tool_not_found": "Tool {tool_name} not found",
            "general_error": "Error: {error}"
        },
        "default_responses": {
            "no_tool_needed": "No tool needed for this request."
        }
    }


@pytest.fixture
def temp_config_file(sample_config):
    """Create a temporary config file for testing."""
    with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
        json.dump(sample_config, f, indent=2)
        temp_path = f.name
    
    yield temp_path
    
    # Cleanup
    if os.path.exists(temp_path):
        os.unlink(temp_path)


@pytest.fixture
def temp_context_dir():
    """Create a temporary directory for context storage."""
    with tempfile.TemporaryDirectory() as temp_dir:
        yield temp_dir


@pytest.fixture
def mock_tool_registry():
    """Create a mock tool registry with test tools."""
    registry = ToolRegistry()
    
    def mock_execute(params):
        return f"Mock result for query: {params.get('query', 'no query')}"
    
    test_tool = Tool(
        name="test_tool",
        description="A test tool",
        execute_func=mock_execute,
        schema={"type": "object", "properties": {"query": {"type": "string"}}}
    )
    
    registry.register_tool(test_tool)
    return registry


@pytest.fixture
def mock_context_manager(temp_context_dir):
    """Create a context manager with temporary storage."""
    context_path = Path(temp_context_dir) / "test_history.json"
    return ContextManager(str(context_path))


@pytest.fixture(autouse=True)
def setup_test_environment():
    """Set up test environment variables."""
    original_env = os.environ.copy()
    
    # Set test environment variables
    os.environ['OPENAI_API_KEY'] = 'test-api-key'
    os.environ['AI_ORCHESTRATOR_MOCK_MODE'] = 'true'
    
    yield
    
    # Restore original environment
    os.environ.clear()
    os.environ.update(original_env)


@pytest.fixture
def mock_successful_llm_response():
    """Mock a successful LLM response for tool selection."""
    return {
        "raw_response": '{"selected_tool": "test_tool", "parameters": {"query": "test query"}, "reasoning": "Using test tool", "direct_response": ""}',
        "parsed_response": {
            "selected_tool": "test_tool",
            "parameters": {"query": "test query"},
            "reasoning": "Using test tool",
            "direct_response": ""
        },
        "used_model": "gpt-4o-mini",
        "error": None
    }


@pytest.fixture
def mock_direct_response():
    """Mock a direct response (no tool needed)."""
    return {
        "raw_response": '{"selected_tool": "none", "parameters": {}, "reasoning": "Direct answer", "direct_response": "This is a direct answer"}',
        "parsed_response": {
            "selected_tool": "none",
            "parameters": {},
            "reasoning": "Direct answer",
            "direct_response": "This is a direct answer"
        },
        "used_model": "gpt-4o-mini",
        "error": None
    }
